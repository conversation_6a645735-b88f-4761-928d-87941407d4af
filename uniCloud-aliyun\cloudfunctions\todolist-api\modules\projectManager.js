/**
 * 项目管理模块
 * 提供项目的CRUD操作和相关功能
 */

const { API_CONFIG, PROJECT_CONFIG, ERROR_CODES } = require('../config.js')
const { 
	createSuccessResponse, 
	createErrorResponse, 
	validateParams,
	simplifyProjectData,
	removeEmptyFields
} = require('../utils.js')

/**
 * 创建项目管理器
 * @param {object} context - 云对象上下文
 * @param {object} authManager - 认证管理器实例
 * @returns {object} 项目管理器实例
 */
function createProjectManager(context, authManager) {
	return {
		/**
		 * 获取项目列表
		 * @param {string} keyword - 关键词筛选
		 * @param {boolean} includeClosed - 是否包含已关闭的项目
		 * @returns {object} 项目列表
		 */
		async getProjects(keyword = null, includeClosed = false) {
			try {
				const batchResult = await authManager.getBatchData()
				if (batchResult.errCode) {
					return batchResult
				}

				let { projects } = batchResult.data
				let filteredProjects = []

				for (const project of projects) {
					// 是否包含已关闭的项目
					if (!includeClosed && project.closed) continue

					// 关键词筛选
					if (keyword) {
						const searchText = `${project.name || ''}`.toLowerCase()
						if (!searchText.includes(keyword.toLowerCase())) continue
					}

					// 简化项目数据
					const simplifiedProject = simplifyProjectData(project)
					filteredProjects.push(simplifiedProject)
				}

				return createSuccessResponse('获取项目列表成功', filteredProjects)

			} catch (error) {
				console.error('获取项目列表异常：', error)
				return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '获取项目列表失败', error)
			}
		},

		/**
		 * 创建项目
		 * @param {string} name - 项目名称
		 * @param {string} color - 项目颜色
		 * @param {string} kind - 项目类型（TASK/NOTE）
		 * @returns {object} 创建结果
		 */
		async createProject(name, color = "#3498db", kind = "TASK") {
			// 参数校验
			const validation = validateParams({ name }, ['name'])
			if (validation) return validation

			try {
				// 准备项目数据
				const projectData = {
					name: name,
					color: color,
					kind: kind,
					closed: false
				}

				// 移除空值字段
				const cleanProjectData = removeEmptyFields(projectData)

				// 发送创建请求
				const result = await authManager._request('POST', API_CONFIG.PROJECT_URL, cleanProjectData)
				if (result.errCode) {
					return result
				}

				// 返回简化后的项目数据
				const simplifiedProject = simplifyProjectData(result.data)
				
				return createSuccessResponse('项目创建成功', simplifiedProject)

			} catch (error) {
				console.error('创建项目异常：', error)
				return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '创建项目失败', error)
			}
		},

		/**
		 * 更新项目
		 * @param {string} projectId - 项目ID
		 * @param {object} updateData - 更新数据
		 * @returns {object} 更新结果
		 */
		async updateProject(projectId, updateData) {
			// 参数校验
			const validation = validateParams({ projectId }, ['projectId'])
			if (validation) return validation

			if (!updateData || typeof updateData !== 'object') {
				return createErrorResponse(ERROR_CODES.PARAM_INVALID, '更新数据不能为空')
			}

			try {
				// 移除空值字段
				const cleanUpdateData = removeEmptyFields(updateData)

				// 发送更新请求
				const result = await authManager._request('PUT', `${API_CONFIG.PROJECT_URL}/${projectId}`, cleanUpdateData)
				if (result.errCode) {
					return result
				}

				// 返回简化后的项目数据
				const simplifiedProject = simplifyProjectData(result.data)
				
				return createSuccessResponse('项目更新成功', simplifiedProject)

			} catch (error) {
				console.error('更新项目异常：', error)
				return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '更新项目失败', error)
			}
		},

		/**
		 * 删除项目
		 * @param {string} projectId - 项目ID
		 * @returns {object} 删除结果
		 */
		async deleteProject(projectId) {
			// 参数校验
			const validation = validateParams({ projectId }, ['projectId'])
			if (validation) return validation

			try {
				// 发送删除请求
				const result = await authManager._request('DELETE', `${API_CONFIG.PROJECT_URL}/${projectId}`)
				if (result.errCode) {
					return result
				}

				return createSuccessResponse('项目删除成功', { projectId })

			} catch (error) {
				console.error('删除项目异常：', error)
				return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '删除项目失败', error)
			}
		},

		/**
		 * 关闭项目
		 * @param {string} projectId - 项目ID
		 * @returns {object} 关闭结果
		 */
		async closeProject(projectId) {
			return await this.updateProject(projectId, { closed: true })
		},

		/**
		 * 重新打开项目
		 * @param {string} projectId - 项目ID
		 * @returns {object} 重新打开结果
		 */
		async reopenProject(projectId) {
			return await this.updateProject(projectId, { closed: false })
		},

		/**
		 * 获取单个项目详情
		 * @param {string} projectId - 项目ID
		 * @returns {object} 项目详情
		 */
		async getProject(projectId) {
			// 参数校验
			const validation = validateParams({ projectId }, ['projectId'])
			if (validation) return validation

			try {
				// 发送获取请求
				const result = await authManager._request('GET', `${API_CONFIG.PROJECT_URL}/${projectId}`)
				if (result.errCode) {
					return result
				}

				// 返回简化后的项目数据
				const simplifiedProject = simplifyProjectData(result.data)
				
				return createSuccessResponse('获取项目详情成功', simplifiedProject)

			} catch (error) {
				console.error('获取项目详情异常：', error)
				return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '获取项目详情失败', error)
			}
		}
	}
}

module.exports = createProjectManager
