/**
 * 任务管理模块
 * 提供任务的CRUD操作和相关功能
 */

const { API_CONFIG, TASK_CONFIG, ERROR_CODES } = require("../config.js");
const {
	createSuccessResponse,
	createErrorResponse,
	validateParams,
	formatDateForApi,
	simplifyTaskData,
	removeEmptyFields,
} = require("../utils.js");

/**
 * 创建任务管理器
 * @param {object} context - 云对象上下文
 * @param {object} authManager - 认证管理器实例
 * @returns {object} 任务管理器实例
 */
function createTaskManager(context, authManager) {
	return {
		/**
		 * 获取任务列表
		 * @param {string} mode - 任务模式（all/today/yesterday/recent_7_days）
		 * @param {string} keyword - 关键词筛选
		 * @param {number} priority - 优先级筛选（0-最低, 1-低, 3-中, 5-高）
		 * @param {string} projectName - 项目名称筛选
		 * @param {boolean} completed - 是否已完成
		 * @returns {object} 任务列表
		 */
		async getTasks(
			mode = "all",
			keyword = null,
			priority = null,
			projectName = null,
			completed = null
		) {
			try {
				const batchResult = await authManager.getBatchData();
				if (batchResult.errCode) {
					return batchResult;
				}

				const { tasks, projects } = batchResult.data;
				let filteredTasks = [];

				// 处理时间筛选
				const now = new Date();
				const today = new Date(
					now.getFullYear(),
					now.getMonth(),
					now.getDate()
				);
				const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
				const sevenDaysAgo = new Date(
					today.getTime() - 7 * 24 * 60 * 60 * 1000
				);

				for (const task of tasks) {
					// 只处理文本类型的任务
					if (task.kind !== TASK_CONFIG.KIND.TEXT) continue;

					// 时间筛选
					if (mode !== TASK_CONFIG.FILTER_MODE.ALL) {
						const taskDate = task.modifiedTime
							? new Date(task.modifiedTime)
							: null;
						if (!taskDate) continue;

						const taskDay = new Date(
							taskDate.getFullYear(),
							taskDate.getMonth(),
							taskDate.getDate()
						);

						if (
							mode === TASK_CONFIG.FILTER_MODE.TODAY &&
							taskDay.getTime() !== today.getTime()
						)
							continue;
						if (
							mode === TASK_CONFIG.FILTER_MODE.YESTERDAY &&
							taskDay.getTime() !== yesterday.getTime()
						)
							continue;
						if (
							mode === TASK_CONFIG.FILTER_MODE.RECENT_7_DAYS &&
							taskDay < sevenDaysAgo
						)
							continue;
					}

					// 完成状态筛选
					if (completed !== null) {
						const isCompleted =
							task.status === TASK_CONFIG.STATUS.COMPLETED || task.isCompleted;
						if (completed !== isCompleted) continue;
					}

					// 优先级筛选
					if (priority !== null && task.priority !== priority) continue;

					// 项目名称筛选
					if (projectName) {
						const project = projects.find((p) => p.id === task.projectId);
						if (!project || project.name !== projectName) continue;
					}

					// 关键词筛选
					if (keyword) {
						const searchText = `${task.title || ""} ${
							task.content || ""
						}`.toLowerCase();
						if (!searchText.includes(keyword.toLowerCase())) continue;
					}

					// 简化任务数据
					const simplifiedTask = simplifyTaskData(task, projects);
					filteredTasks.push(simplifiedTask);
				}

				return createSuccessResponse("获取任务列表成功", filteredTasks);
			} catch (error) {
				console.error("获取任务列表异常：", error);
				return createErrorResponse(
					ERROR_CODES.UNKNOWN_ERROR,
					error.message || "获取任务列表失败",
					error
				);
			}
		},

		/**
		 * 创建任务
		 * @param {string} title - 任务标题
		 * @param {string} content - 任务内容
		 * @param {number} priority - 优先级（0-最低, 1-低, 3-中, 5-高）
		 * @param {string} projectName - 项目名称
		 * @param {Array} tagNames - 标签名称数组
		 * @param {string} startDate - 开始日期（YYYY-MM-DD HH:mm）
		 * @param {string} dueDate - 截止日期（YYYY-MM-DD HH:mm）
		 * @param {boolean} isAllDay - 是否全天
		 * @param {string} reminder - 提醒时间
		 * @param {string} kind - 任务类型（默认TEXT）
		 * @returns {object} 创建结果
		 */
		async createTask(
			title,
			content = "",
			priority = 0,
			projectName = null,
			tagNames = null,
			startDate = null,
			dueDate = null,
			isAllDay = false,
			reminder = null,
			kind = "TEXT"
		) {
			// 参数校验
			const validation = validateParams({ title }, ["title"]);
			if (validation) return validation;

			try {
				// 获取项目数据
				const batchResult = await authManager.getBatchData();
				if (batchResult.errCode) {
					return batchResult;
				}

				const { projects } = batchResult.data;
				let projectId = null;

				// 查找项目ID
				if (projectName) {
					const project = projects.find((p) => p.name === projectName);
					if (!project) {
						return createErrorResponse(
							ERROR_CODES.PROJECT_NOT_FOUND,
							`未找到项目: ${projectName}`
						);
					}
					projectId = project.id;
				}

				// 准备任务数据
				const taskData = {
					title: title,
					content: content,
					priority: priority,
					projectId: projectId,
					tags: tagNames || [],
					isAllDay: isAllDay,
					reminder: reminder,
					status: TASK_CONFIG.STATUS.ACTIVE,
					kind: kind,
				};

				// 处理日期
				if (startDate) {
					taskData.startDate = formatDateForApi(startDate);
				}

				if (dueDate) {
					taskData.dueDate = formatDateForApi(dueDate);
				}

				// 移除空值字段
				const cleanTaskData = removeEmptyFields(taskData);

				// 发送创建请求
				const result = await authManager._request(
					"POST",
					API_CONFIG.TASK_URL,
					cleanTaskData
				);
				if (result.errCode) {
					return result;
				}

				// 返回简化后的任务数据
				const simplifiedTask = simplifyTaskData(result.data, projects);

				return createSuccessResponse("任务创建成功", simplifiedTask);
			} catch (error) {
				console.error("创建任务异常：", error);
				return createErrorResponse(
					ERROR_CODES.UNKNOWN_ERROR,
					error.message || "创建任务失败",
					error
				);
			}
		},

		/**
		 * 更新任务
		 * @param {string} taskId - 任务ID
		 * @param {object} updateData - 更新数据
		 * @returns {object} 更新结果
		 */
		async updateTask(taskId, updateData) {
			// 参数校验
			const validation = validateParams({ taskId }, ["taskId"]);
			if (validation) return validation;

			if (!updateData || typeof updateData !== "object") {
				return createErrorResponse(
					ERROR_CODES.PARAM_INVALID,
					"更新数据不能为空"
				);
			}

			try {
				// 获取项目数据（如果需要项目名称转换）
				let projects = [];
				if (updateData.projectName) {
					const batchResult = await authManager.getBatchData();
					if (batchResult.errCode) {
						return batchResult;
					}
					projects = batchResult.data.projects;

					// 查找项目ID
					const project = projects.find(
						(p) => p.name === updateData.projectName
					);
					if (!project) {
						return createErrorResponse(
							ERROR_CODES.PROJECT_NOT_FOUND,
							`未找到项目: ${updateData.projectName}`
						);
					}
					updateData.projectId = project.id;
					delete updateData.projectName;
				}

				// 处理日期格式
				if (updateData.startDate) {
					updateData.startDate = formatDateForApi(updateData.startDate);
				}
				if (updateData.dueDate) {
					updateData.dueDate = formatDateForApi(updateData.dueDate);
				}

				// 移除空值字段
				const cleanUpdateData = removeEmptyFields(updateData);

				// 发送更新请求
				const result = await authManager._request(
					"PUT",
					`${API_CONFIG.TASK_URL}/${taskId}`,
					cleanUpdateData
				);
				if (result.errCode) {
					return result;
				}

				// 返回简化后的任务数据
				const simplifiedTask = simplifyTaskData(result.data, projects);

				return createSuccessResponse("任务更新成功", simplifiedTask);
			} catch (error) {
				console.error("更新任务异常：", error);
				return createErrorResponse(
					ERROR_CODES.UNKNOWN_ERROR,
					error.message || "更新任务失败",
					error
				);
			}
		},

		/**
		 * 删除任务
		 * @param {string} taskId - 任务ID
		 * @returns {object} 删除结果
		 */
		async deleteTask(taskId) {
			// 参数校验
			const validation = validateParams({ taskId }, ["taskId"]);
			if (validation) return validation;

			try {
				// 发送删除请求
				const result = await authManager._request(
					"DELETE",
					`${API_CONFIG.TASK_URL}/${taskId}`
				);
				if (result.errCode) {
					return result;
				}

				return createSuccessResponse("任务删除成功", { taskId });
			} catch (error) {
				console.error("删除任务异常：", error);
				return createErrorResponse(
					ERROR_CODES.UNKNOWN_ERROR,
					error.message || "删除任务失败",
					error
				);
			}
		},

		/**
		 * 完成任务
		 * @param {string} taskId - 任务ID
		 * @returns {object} 完成结果
		 */
		async completeTask(taskId) {
			return await this.updateTask(taskId, {
				status: TASK_CONFIG.STATUS.COMPLETED,
				isCompleted: true,
			});
		},

		/**
		 * 取消完成任务
		 * @param {string} taskId - 任务ID
		 * @returns {object} 取消完成结果
		 */
		async uncompleteTask(taskId) {
			return await this.updateTask(taskId, {
				status: TASK_CONFIG.STATUS.ACTIVE,
				isCompleted: false,
			});
		},

		/**
		 * 获取单个任务详情
		 * @param {string} taskId - 任务ID
		 * @returns {object} 任务详情
		 */
		async getTask(taskId) {
			// 参数校验
			const validation = validateParams({ taskId }, ["taskId"]);
			if (validation) return validation;

			try {
				// 发送获取请求
				const result = await authManager._request(
					"GET",
					`${API_CONFIG.TASK_URL}/${taskId}`
				);
				if (result.errCode) {
					return result;
				}

				// 获取项目数据用于简化任务信息
				const batchResult = await authManager.getBatchData();
				const projects = batchResult.errCode ? [] : batchResult.data.projects;

				// 返回简化后的任务数据
				const simplifiedTask = simplifyTaskData(result.data, projects);

				return createSuccessResponse("获取任务详情成功", simplifiedTask);
			} catch (error) {
				console.error("获取任务详情异常：", error);
				return createErrorResponse(
					ERROR_CODES.UNKNOWN_ERROR,
					error.message || "获取任务详情失败",
					error
				);
			}
		},

		/**
		 * 批量操作任务
		 * @param {Array} taskIds - 任务ID数组
		 * @param {string} action - 操作类型（complete/uncomplete/delete）
		 * @returns {object} 批量操作结果
		 */
		async batchOperateTasks(taskIds, action) {
			// 参数校验
			const validation = validateParams({ taskIds, action }, [
				"taskIds",
				"action",
			]);
			if (validation) return validation;

			if (!Array.isArray(taskIds) || taskIds.length === 0) {
				return createErrorResponse(
					ERROR_CODES.PARAM_INVALID,
					"任务ID数组不能为空"
				);
			}

			const validActions = ["complete", "uncomplete", "delete"];
			if (!validActions.includes(action)) {
				return createErrorResponse(
					ERROR_CODES.PARAM_INVALID,
					`无效的操作类型: ${action}`
				);
			}

			try {
				const results = [];
				const errors = [];

				for (const taskId of taskIds) {
					let result;
					switch (action) {
						case "complete":
							result = await this.completeTask(taskId);
							break;
						case "uncomplete":
							result = await this.uncompleteTask(taskId);
							break;
						case "delete":
							result = await this.deleteTask(taskId);
							break;
					}

					if (result.errCode) {
						errors.push({ taskId, error: result });
					} else {
						results.push({ taskId, result: result.data });
					}
				}

				return createSuccessResponse("批量操作完成", {
					successCount: results.length,
					errorCount: errors.length,
					results: results,
					errors: errors,
				});
			} catch (error) {
				console.error("批量操作任务异常：", error);
				return createErrorResponse(
					ERROR_CODES.UNKNOWN_ERROR,
					error.message || "批量操作任务失败",
					error
				);
			}
		},
	};
}

module.exports = createTaskManager;
