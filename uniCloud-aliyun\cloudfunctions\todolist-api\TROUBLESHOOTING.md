# 滴答清单API云对象故障排除指南

## 常见错误及解决方案

### 1. "Method name is required" 错误

#### 错误描述
```
Error: Method name is required
```

#### 错误原因
这个错误通常发生在以下情况：
1. **错误地使用了 `uniCloud.callFunction` 调用云对象**
2. 云对象的 `getMethodName()` 返回空值
3. 调用云对象时没有指定具体的方法名

#### 解决方案

**❌ 错误的调用方式：**
```javascript
// 错误：使用 callFunction 调用云对象
const result = await uniCloud.callFunction({
  name: 'todolist-api',
  data: {
    action: 'login',
    params: {
      username: 'your-phone',
      password: 'your-password'
    }
  }
})
```

**✅ 正确的调用方式：**
```javascript
// 正确：使用 importObject 调用云对象
const todolistApi = uniCloud.importObject('todolist-api')
const result = await todolistApi.login('your-phone', 'your-password', true)
```

#### 详细说明

1. **云对象 vs 云函数**
   - **云对象**：使用 `uniCloud.importObject()` 调用，文件名为 `index.obj.js`
   - **云函数**：使用 `uniCloud.callFunction()` 调用，文件名为 `index.js`

2. **云对象调用语法**
   ```javascript
   // 导入云对象
   const cloudObj = uniCloud.importObject('云对象名称')
   
   // 调用云对象方法
   const result = await cloudObj.methodName(param1, param2, ...)
   ```

3. **云函数调用语法**
   ```javascript
   // 调用云函数
   const result = await uniCloud.callFunction({
     name: '云函数名称',
     data: {
       // 参数数据
     }
   })
   ```

### 2. 其他常见错误

#### 网络访问被拒绝
```
Error: Network access denied
```

**解决方案：**
1. 在 uniCloud 控制台配置网络访问权限
2. 添加滴答清单API域名到白名单：
   - `api.dida365.com`
   - `dida365.com`

#### Token失效
```
Error: Unauthorized
```

**解决方案：**
1. 重新调用 `login` 方法获取新的token
2. 检查登录凭证是否正确
3. 确认Cookie解析逻辑正确

#### 请求超时
```
Error: Request timeout
```

**解决方案：**
1. 检查网络连接状况
2. 在 `config.js` 中调整 `TIMEOUT` 配置
3. 重试请求

## 正确的使用示例

### 1. 基础认证流程
```javascript
// 导入云对象
const todolistApi = uniCloud.importObject('todolist-api')

try {
  // 登录
  const loginResult = await todolistApi.login('13800138000', 'password', true)
  
  if (loginResult.errCode) {
    console.error('登录失败：', loginResult.errMsg)
    return
  }
  
  console.log('登录成功，token：', loginResult.data.token)
  
  // 获取任务列表
  const tasksResult = await todolistApi.getTasks()
  
  if (!tasksResult.errCode) {
    console.log('任务列表：', tasksResult.data)
  }
  
} catch (error) {
  console.error('调用异常：', error.message)
}
```

### 2. 任务管理示例
```javascript
const todolistApi = uniCloud.importObject('todolist-api')

// 创建任务
const createResult = await todolistApi.createTask(
  '新任务标题',
  '任务内容',
  3, // 中等优先级
  '工作项目',
  ['重要', '紧急'],
  '2024-01-01 09:00',
  '2024-01-01 18:00'
)

// 完成任务
const completeResult = await todolistApi.completeTask('task-id')

// 批量删除任务
const batchResult = await todolistApi.batchOperateTasks(['id1', 'id2'], 'delete')
```

### 3. 项目管理示例
```javascript
const todolistApi = uniCloud.importObject('todolist-api')

// 获取项目列表
const projectsResult = await todolistApi.getProjects()

// 创建项目
const createProjectResult = await todolistApi.createProject(
  '新项目',
  '#3498db', // 蓝色
  'TASK' // 任务类型项目
)

// 关闭项目
const closeResult = await todolistApi.closeProject('project-id')
```

## 调试技巧

### 1. 启用调试日志
云对象会在 `_before` 方法中记录调用日志，包括：
- 调用的方法名
- 客户端IP
- 调用时间

### 2. 检查方法名
如果遇到 "Method name is required" 错误，可以在 `_before` 方法中添加调试代码：
```javascript
_before: function () {
  const methodName = this.getMethodName()
  console.log('调用的方法名：', methodName)
  
  if (!methodName) {
    console.error('方法名为空，请检查调用方式')
  }
}
```

### 3. 验证调用方式
确保使用正确的调用方式：
```javascript
// ✅ 正确
const obj = uniCloud.importObject('todolist-api')
await obj.login(...)

// ❌ 错误
await uniCloud.callFunction({
  name: 'todolist-api',
  data: {...}
})
```

## 联系支持

如果问题仍然存在，请提供以下信息：
1. 完整的错误信息
2. 调用代码示例
3. uniCloud控制台的日志
4. 使用的uniCloud版本和HBuilderX版本

这将帮助快速定位和解决问题。
