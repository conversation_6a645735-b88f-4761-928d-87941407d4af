// 云对象教程：https://uniapp.dcloud.net.cn/uniCloud/cloud-obj
// jsdoc 语法提示教程：https://ask.dcloud.net.cn/docs/#//ask.dcloud.net.cn/article/129

// 导入功能模块
const createAuthManager = require('./modules/authManager.js')
const createTaskManager = require('./modules/taskManager.js')
const createProjectManager = require('./modules/projectManager.js')
const { API_CONFIG } = require('./config.js')

/**
 * 滴答清单API云对象
 * 提供滴答清单的完整API功能，包括认证、任务管理、项目管理等
 * 采用模块化架构，符合uniCloud云对象开发规范
 */
module.exports = {
  _before: function () {
    // 获取客户端信息
    const clientInfo = this.getClientInfo()
    const methodName = this.getMethodName()

    // 检查方法名是否存在
    if (!methodName) {
      console.error('错误：方法名为空。请确保使用 uniCloud.importObject() 而不是 uniCloud.callFunction() 来调用云对象')
      throw new Error(
        'Method name is required. Please use uniCloud.importObject() instead of uniCloud.callFunction() to call cloud objects.'
      )
    }

    // 日志记录
    console.log(`[${new Date().toISOString()}] 调用方法：${methodName}, 客户端：${clientInfo.clientIP}`)

    // 初始化全局变量
    this.BASE_URL = API_CONFIG.BASE_URL
    this.token = null
    this.headers = {}
    this.debug = true

    // 初始化功能模块
    this.authManager = createAuthManager(this)
    this.taskManager = createTaskManager(this, this.authManager)
    this.projectManager = createProjectManager(this, this.authManager)
  },

  // ==================== 认证相关方法 ====================

  /**
   * 用户登录认证
   * @param {string} username - 用户名（手机号或邮箱）
   * @param {string} password - 用户密码
   * @param {boolean} isPhone - 是否使用手机号登录，默认true
   * @returns {object} 登录结果
   */
  async login(username, password, isPhone = true) {
    return await this.authManager.login(username, password, isPhone)
  },

  /**
   * 初始化API（使用已有token）
   * @param {string} token - 访问令牌
   * @returns {object} 初始化结果
   */
  async initWithToken(token) {
    return await this.authManager.initWithToken(token)
  },

  /**
   * 获取所有基础数据（任务、项目、标签）
   * @returns {object} 基础数据
   */
  async getBatchData() {
    return await this.authManager.getBatchData()
  },

  // ==================== 任务管理方法 ====================

  /**
   * 获取任务列表
   * @param {string} mode - 任务模式（all/today/yesterday/recent_7_days）
   * @param {string} keyword - 关键词筛选
   * @param {number} priority - 优先级筛选（0-最低, 1-低, 3-中, 5-高）
   * @param {string} projectName - 项目名称筛选
   * @param {boolean} completed - 是否已完成
   * @returns {object} 任务列表
   */
  async getTasks(mode = 'all', keyword = null, priority = null, projectName = null, completed = null) {
    return await this.taskManager.getTasks(mode, keyword, priority, projectName, completed)
  },

  /**
   * 创建任务
   * @param {string} title - 任务标题
   * @param {string} content - 任务内容
   * @param {number} priority - 优先级（0-最低, 1-低, 3-中, 5-高）
   * @param {string} projectName - 项目名称
   * @param {Array} tagNames - 标签名称数组
   * @param {string} startDate - 开始日期（YYYY-MM-DD HH:mm）
   * @param {string} dueDate - 截止日期（YYYY-MM-DD HH:mm）
   * @param {boolean} isAllDay - 是否全天
   * @param {string} reminder - 提醒时间
   * @param {string} kind - 任务类型（默认TEXT）
   * @returns {object} 创建结果
   */
  async createTask(
    title,
    content = '',
    priority = 0,
    projectName = null,
    tagNames = null,
    startDate = null,
    dueDate = null,
    isAllDay = false,
    reminder = null,
    kind = 'TEXT'
  ) {
    return await this.taskManager.createTask(
      title,
      content,
      priority,
      projectName,
      tagNames,
      startDate,
      dueDate,
      isAllDay,
      reminder,
      kind
    )
  },

  /**
   * 更新任务
   * @param {string} taskId - 任务ID
   * @param {object} updateData - 更新数据
   * @returns {object} 更新结果
   */
  async updateTask(taskId, updateData) {
    return await this.taskManager.updateTask(taskId, updateData)
  },

  /**
   * 删除任务
   * @param {string} taskId - 任务ID
   * @returns {object} 删除结果
   */
  async deleteTask(taskId) {
    return await this.taskManager.deleteTask(taskId)
  },

  /**
   * 完成任务
   * @param {string} taskId - 任务ID
   * @returns {object} 完成结果
   */
  async completeTask(taskId) {
    return await this.taskManager.completeTask(taskId)
  },

  /**
   * 取消完成任务
   * @param {string} taskId - 任务ID
   * @returns {object} 取消完成结果
   */
  async uncompleteTask(taskId) {
    return await this.taskManager.uncompleteTask(taskId)
  },

  /**
   * 获取单个任务详情
   * @param {string} taskId - 任务ID
   * @returns {object} 任务详情
   */
  async getTask(taskId) {
    return await this.taskManager.getTask(taskId)
  },

  /**
   * 批量操作任务
   * @param {Array} taskIds - 任务ID数组
   * @param {string} action - 操作类型（complete/uncomplete/delete）
   * @returns {object} 批量操作结果
   */
  async batchOperateTasks(taskIds, action) {
    return await this.taskManager.batchOperateTasks(taskIds, action)
  },

  // ==================== 项目管理方法 ====================

  /**
   * 获取项目列表
   * @param {string} keyword - 关键词筛选
   * @param {boolean} includeClosed - 是否包含已关闭的项目
   * @returns {object} 项目列表
   */
  async getProjects(keyword = null, includeClosed = false) {
    return await this.projectManager.getProjects(keyword, includeClosed)
  },

  /**
   * 创建项目
   * @param {string} name - 项目名称
   * @param {string} color - 项目颜色
   * @param {string} kind - 项目类型（TASK/NOTE）
   * @returns {object} 创建结果
   */
  async createProject(name, color = '#3498db', kind = 'TASK') {
    return await this.projectManager.createProject(name, color, kind)
  },

  /**
   * 更新项目
   * @param {string} projectId - 项目ID
   * @param {object} updateData - 更新数据
   * @returns {object} 更新结果
   */
  async updateProject(projectId, updateData) {
    return await this.projectManager.updateProject(projectId, updateData)
  },

  /**
   * 删除项目
   * @param {string} projectId - 项目ID
   * @returns {object} 删除结果
   */
  async deleteProject(projectId) {
    return await this.projectManager.deleteProject(projectId)
  },

  /**
   * 关闭项目
   * @param {string} projectId - 项目ID
   * @returns {object} 关闭结果
   */
  async closeProject(projectId) {
    return await this.projectManager.closeProject(projectId)
  },

  /**
   * 重新打开项目
   * @param {string} projectId - 项目ID
   * @returns {object} 重新打开结果
   */
  async reopenProject(projectId) {
    return await this.projectManager.reopenProject(projectId)
  },

  /**
   * 获取单个项目详情
   * @param {string} projectId - 项目ID
   * @returns {object} 项目详情
   */
  async getProject(projectId) {
    return await this.projectManager.getProject(projectId)
  },
}
