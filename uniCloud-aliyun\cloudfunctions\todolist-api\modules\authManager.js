/**
 * 认证管理模块
 * 提供用户登录、token管理、HTTP请求封装等功能
 */

const { API_CONFIG, ERROR_CODES } = require('../config.js')
const { 
	createSuccessResponse, 
	createErrorResponse, 
	validateParams,
	parseHttpResponse,
	extractTokenFromCookies
} = require('../utils.js')

/**
 * 创建认证管理器
 * @param {object} context - 云对象上下文
 * @returns {object} 认证管理器实例
 */
function createAuthManager(context) {
	return {
		/**
		 * 用户登录认证
		 * @param {string} username - 用户名（手机号或邮箱）
		 * @param {string} password - 用户密码
		 * @param {boolean} isPhone - 是否使用手机号登录，默认true
		 * @returns {object} 登录结果
		 */
		async login(username, password, isPhone = true) {
			// 参数校验
			const validation = validateParams({ username, password }, ['username', 'password'])
			if (validation) return validation

			try {
				const loginUrl = API_CONFIG.LOGIN_URL

				// 根据登录类型选择不同的字段名
				const loginData = isPhone ? {
					password: password,
					phone: username
				} : {
					password: password,
					email: username
				}

				const headers = API_CONFIG.LOGIN_HEADERS

				const loginType = isPhone ? "手机号" : "邮箱"
				if (context.debug) {
					console.log(`尝试使用${loginType}登录账号：${username}`)
				}

				// 发送登录请求
				const response = await uniCloud.httpclient.request(loginUrl, {
					method: 'POST',
					headers: headers,
					data: loginData,
					timeout: API_CONFIG.TIMEOUT
				})

				if (response.status !== 200) {
					return createErrorResponse(
						ERROR_CODES.LOGIN_ERROR,
						`登录失败，状态码：${response.status}`,
						response.data
					)
				}

				// 解析响应数据
				let responseData
				try {
					responseData = typeof response.data === 'string'
						? JSON.parse(response.data)
						: response.data
				} catch (parseError) {
					return createErrorResponse(
						ERROR_CODES.PARSE_ERROR,
						'响应数据解析失败',
						response.data
					)
				}

				// 从cookies中获取token
				let token = null
				if (response.headers && response.headers['set-cookie']) {
					token = extractTokenFromCookies(response.headers['set-cookie'])
				}

				// 如果cookies中没有token，尝试从响应体中获取
				if (!token && responseData && typeof responseData === 'object' && responseData.token) {
					token = responseData.token
				}

				if (!token) {
					return createErrorResponse(
						ERROR_CODES.TOKEN_NOT_FOUND,
						'登录成功但未获取到token',
						responseData
					)
				}

				// 设置token和请求头到上下文
				context.token = token
				context.headers = {
					...API_CONFIG.DEFAULT_HEADERS,
					"Cookie": `t=${token}`
				}

				if (context.debug) {
					console.log(`从cookies中获取到token: ${token.substring(0, 10)}...`)
				}

				return createSuccessResponse("登录成功", {
					token: token,
					loginType: loginType
				})

			} catch (error) {
				console.error('登录异常：', error)

				if (error.code === 'TIMEOUT') {
					return createErrorResponse(ERROR_CODES.TIMEOUT_ERROR, '登录请求超时，请稍后重试')
				}

				return createErrorResponse(ERROR_CODES.NETWORK_ERROR, error.message || '登录失败', error)
			}
		},

		/**
		 * 初始化API（使用已有token）
		 * @param {string} token - 访问令牌
		 * @returns {object} 初始化结果
		 */
		async initWithToken(token) {
			const validation = validateParams({ token }, ['token'])
			if (validation) return validation

			context.token = token
			context.headers = {
				...API_CONFIG.DEFAULT_HEADERS,
				"Cookie": `t=${token}`
			}

			return createSuccessResponse("API初始化成功")
		},

		/**
		 * 发送HTTP请求的通用方法
		 * @param {string} method - HTTP方法
		 * @param {string} endpoint - API端点
		 * @param {object} data - 请求数据
		 * @param {object} params - URL参数
		 * @returns {object} 请求结果
		 */
		async _request(method, endpoint, data = null, params = null) {
			if (!context.token) {
				return createErrorResponse(ERROR_CODES.UNAUTHORIZED, '请先登录或初始化token')
			}

			const url = `${context.BASE_URL}${endpoint}`
			
			try {
				const requestOptions = {
					method: method,
					headers: context.headers,
					timeout: API_CONFIG.TIMEOUT
				}

				if (data) {
					requestOptions.data = data
				}

				if (params) {
					requestOptions.params = params
				}

				const response = await uniCloud.httpclient.request(url, requestOptions)
				return parseHttpResponse(response)

			} catch (error) {
				console.error('HTTP请求异常：', error)

				if (error.code === 'TIMEOUT') {
					return createErrorResponse(ERROR_CODES.TIMEOUT_ERROR, '请求超时，请稍后重试')
				}

				return createErrorResponse(ERROR_CODES.NETWORK_ERROR, error.message || 'HTTP请求失败', error)
			}
		},

		/**
		 * 获取所有基础数据（任务、项目、标签）
		 * @returns {object} 基础数据
		 */
		async getBatchData() {
			const result = await this._request('GET', API_CONFIG.BATCH_DATA_URL)
			if (result.errCode) {
				return result
			}

			return createSuccessResponse('获取基础数据成功', {
				tasks: result.data.syncTaskBean?.update || [],
				projects: result.data.projectProfiles || [],
				tags: result.data.tags || []
			})
		}
	}
}

module.exports = createAuthManager
